import re
import json
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
from concurrent.futures import ProcessPoolExecutor, as_completed
from bs4 import BeautifulSoup

class BBBProfileExtractor:
    """
    Production-ready BBB business profile data extractor.

    Extracts comprehensive business information from BBB profile pages using a
    multi-layered approach with fallback mechanisms for robust data extraction.
    It prioritizes structured data (JSON) and falls back to HTML parsing.
    """

    def __init__(self):
        """Initializes the extractor."""
        self.data: Dict[str, Any] = {}

    def extract_profile(self, html_content: str) -> Dict[str, Any]:
        """
        Extracts business information from BBB profile HTML.

        Args:
            html_content: Raw HTML content of the BBB profile page.

        Returns:
            A dictionary containing the extracted business data.

        Raises:
            ValueError: If HTML content is invalid or empty.
        """
        if not html_content or not html_content.strip():
            raise ValueError("HTML content cannot be empty")

        self.data = {
            "business_name": None, "business_type": None, "accreditation": None,
            "phone": None, "address": None, "rating": None,
            "business_details": {}, "management": {}, "business_categories": [],
            "additional_info": {}
        }
        
        soup = BeautifulSoup(html_content, 'html.parser')

        # Priority 1: Extract from embedded 'webDigitalData' JavaScript object.
        self._populate_from_web_digital_data(soup)

        # Priority 2: Extract from JSON-LD structured data.
        self._populate_from_json_ld(soup)

        # Priority 3: Fallback to direct HTML element parsing.
        self._populate_from_html(soup)

        # Final cleanup and formatting.
        self._post_process_data()

        return self.data

    # --------------------------------------------------------------------------
    # Primary Data Extraction Methods (JSON-based)
    # --------------------------------------------------------------------------

    def _populate_from_web_digital_data(self, soup: BeautifulSoup) -> None:
        """Populates data from the 'webDigitalData' JavaScript variable."""
        try:
            script_tag = soup.find('script', string=re.compile(r'var webDigitalData\s*='))
            if not script_tag:
                return

            # Try multiple regex patterns to match the JSON data
            patterns = [
                r'var webDigitalData\s*=\s*({.*?});',  
                r'var webDigitalData\s*=\s*({.*?})(?=</script>)',  
                r'var webDigitalData\s*=\s*({.*?})(?=\s*</script>)',  
                r'var webDigitalData\s*=\s*({.*})',  
            ]

            match = None
            for pattern in patterns:
                match = re.search(pattern, script_tag.string, re.DOTALL)
                if match:
                    break

            if not match:
                return

            web_data = json.loads(match.group(1))
            business_info = web_data.get('business_info', {})
            page_info = web_data.get('page', {})

            self.data['business_name'] = business_info.get('business_name')
            self.data['phone'] = business_info.get('business_phone')

            rating = business_info.get('business_rating')
            if rating:
                self.data['rating'] = 'No Rating' if rating == 'NR' else rating

            accred_status = business_info.get('accredited_status')
            if accred_status:
                self.data['accreditation'] = 'Accredited' if accred_status == 'AB' else 'Not Accredited'

            self.data['business_type'] = page_info.get('site_subsection3')

            self.data['additional_info'].update({
                'business_id': business_info.get('business_id'),
                'business_zip': business_info.get('business_zip'),
                'bbb_id': page_info.get('bbb_id'),
                'has_logo': business_info.get('has_logo', 'N') == 'Y'
            })

        except (json.JSONDecodeError, AttributeError):
            # Fail silently if this high-priority source is missing/malformed.
            pass

    def _populate_from_json_ld(self, soup: BeautifulSoup) -> None:
        """Populates data from JSON-LD <script> tags."""
        try:
            json_ld_scripts = soup.find_all('script', type='application/ld+json')
            for script in json_ld_scripts:
                if not script.string:
                    continue
                
                ld_data = json.loads(script.string)
                items = ld_data if isinstance(ld_data, list) else [ld_data]

                for item in items:
                    item_type = item.get('@type')
                    if item_type == 'LocalBusiness':
                        self._fill_if_empty('business_name', item.get('name'))
                        self._fill_if_empty('phone', item.get('telephone'))
                        if 'foundingDate' in item:
                            self.data['business_details']['Business Started'] = item['foundingDate']
                        
                        if not self.data.get('address') and 'address' in item:
                            addr = item['address']
                            self.data['address'] = ', '.join(filter(None, [
                                addr.get('streetAddress'),
                                addr.get('addressLocality'),
                                addr.get('addressRegion'),
                                addr.get('postalCode')
                            ]))

        except (json.JSONDecodeError, AttributeError):
            pass

    # --------------------------------------------------------------------------
    # Fallback Data Extraction (HTML Parsing)
    # --------------------------------------------------------------------------

    def _populate_from_html(self, soup: BeautifulSoup) -> None:
        """Populates data by parsing HTML elements as a fallback."""
        self._fill_if_empty('business_name', self._find_html_element_text(soup, ['h1[class*="business-name"]', 'h1']))
        self._fill_if_empty('phone', self._find_html_element_text(soup, ['a[href^="tel:"]']))
        self._fill_if_empty('address', self._find_html_element_text(soup, ['div[class*="address"] p'], separator=' '))
        self._fill_if_empty('rating', self._find_html_element_text(soup, ['span[class*="rating"]', 'dt.mr-2 + dd']))

        # Accreditation - Enhanced extraction with multiple fallback methods
        if not self.data.get('accreditation'):
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            if meta_desc and meta_desc.get('content'):
                desc_content = meta_desc['content'].lower()
                if 'bbb accredited since' in desc_content:
                    # Extract the accreditation date
                    date_match = re.search(r'bbb accredited since (\d{1,2}/\d{1,2}/\d{4})', desc_content)
                    if date_match:
                        self.data['accreditation'] = f"Accredited since {date_match.group(1)}"
                    else:
                        self.data['accreditation'] = 'Accredited'
                elif 'accredited' in desc_content and 'not' not in desc_content:
                    self.data['accreditation'] = 'Accredited'

            if not self.data.get('accreditation'):
                accreditation_text = self._find_html_element_text(soup, ['div[class*="accreditation-seal"] p'])
                if accreditation_text:
                    if 'not' in accreditation_text.lower():
                        self.data['accreditation'] = 'Not Accredited'
                    else:
                        self.data['accreditation'] = 'Accredited'

        # Details, Management, and Categories
        self._extract_business_details(soup)
        self._extract_management_info(soup)
        self._extract_business_categories(soup)

    def _extract_business_details(self, soup: BeautifulSoup) -> None:
        """Extracts key-value pairs from the 'Business Details' section."""
        details_container = soup.find(lambda tag: (tag.name in ['h2', 'h3'] and 'Business Details' in tag.text) or (tag.name == 'div' and tag.has_attr('id') and 'details' in tag['id']))
        if not details_container:
            return

        parent_section = details_container.find_parent()
        for dt in parent_section.find_all('dt'):
            key = dt.get_text(strip=True).replace(':', '')
            dd = dt.find_next_sibling('dd')
            if key and dd:
                # Preserve newlines for multi-line values like addresses or resources
                value = dd.get_text(separator='\n', strip=True)
                if key not in self.data['business_details']:
                    self.data['business_details'][key] = value

    def _extract_management_info(self, soup: BeautifulSoup) -> None:
        """Extracts management/contact information."""
        #Note: soup parameter kept for consistency with other extraction methods
        # but not used as data comes from already-extracted business_details
        if self.data['management'].get('name'): 
            return

        management_text = self.data['business_details'].get('Business Management') or \
                          self.data['business_details'].get('Principal Contacts') or \
                          self.data['business_details'].get('Customer Contacts')
        
        if management_text:
            self._parse_management_text(management_text)

    def _parse_management_text(self, text: str) -> None:
        """Parses a string to extract a name and title."""
        parts = [p.strip() for p in text.split(',')]
        if not self.data['management'].get('name'):
            self.data['management']['name'] = parts[0]
        if not self.data['management'].get('title') and len(parts) > 1:
            self.data['management']['title'] = ', '.join(parts[1:])
    
    def _extract_business_categories(self, soup: BeautifulSoup) -> None:
        """Extracts business categories from multiple possible locations."""
        categories = set(self.data['business_categories'])
        
        # From specific category sections
        cat_heading = soup.find(['h2', 'h3'], string=re.compile('Categories', re.I))
        if cat_heading:
            container = cat_heading.find_next(['div', 'ul'])
            if container:
                for link in container.find_all('a'):
                    categories.add(link.get_text(strip=True))
        
        # From business details if available
        if 'Business Categories' in self.data['business_details']:
             for cat in re.split(r'[,;|\n]', self.data['business_details']['Business Categories']):
                 if cat.strip():
                     categories.add(cat.strip())

        self.data['business_categories'] = sorted(list(categories))


    # --------------------------------------------------------------------------
    # Post-Processing and Utility Methods
    # --------------------------------------------------------------------------

    def _post_process_data(self) -> None:
        """Cleans and normalizes the extracted data."""
        if self.data.get('business_name'):
            self.data['business_name'] = self.data['business_name'].split('|')[0].strip()

        if self.data.get('phone'):
            match = re.search(r'\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}', self.data['phone'])
            if match:
                self.data['phone'] = match.group(0)

        if not self.data.get('business_type') and self.data['business_categories']:
            self.data['business_type'] = self.data['business_categories'][0]
            
        # Remove empty keys from dictionaries
        self.data['business_details'] = {k: v for k, v in self.data['business_details'].items() if v and str(v).strip()}
        self.data['additional_info'] = {k: v for k, v in self.data['additional_info'].items() if v}

    def _fill_if_empty(self, key: str, value: Any) -> None:
        """Fills a key in the data dictionary only if it's currently empty."""
        if value and not self.data.get(key):
            self.data[key] = value

    def _find_html_element_text(self, soup: BeautifulSoup, selectors: List[str], separator: str = '') -> Optional[str]:
        """Finds the first matching element from a list of CSS selectors and returns its text."""
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text(separator=separator, strip=True)
        return None

# ------------------------------------------------------------------------------
# OPTIMIZED - Parallel Processing Logic
# ------------------------------------------------------------------------------

def process_single_file(file_path: Path) -> Optional[Dict[str, Any]]:
    """
    Worker function to read and extract data from a single HTML file.
    Designed to be run in a separate process.
    
    Args:
        file_path: The path object of the HTML file to process.

    Returns:
        A dictionary of the extracted business data, or None if an error occurs.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        extractor = BBBProfileExtractor()
        business_data = extractor.extract_profile(html_content)
        business_data['source_file'] = file_path.name
        return business_data
    except Exception as e:
        print(f"Error processing file {file_path.name}: {e}")
        return None

def process_directory_in_parallel(input_dir: str, output_file: str) -> None:
    """
    Finds all HTML files in a directory, processes them in parallel using a
    process pool, and saves the consolidated results to a single JSON file.

    Args:
        input_dir: Path to the directory containing HTML files.
        output_file: Path to save the consolidated JSON data.
    """
    input_path = Path(input_dir)
    if not input_path.is_dir():
        raise FileNotFoundError(f"Input directory not found: {input_dir}")

    html_files = list(input_path.glob("*.html"))
    if not html_files:
        print(f"No HTML files found in directory: {input_dir}")
        return

    print(f"Found {len(html_files)} HTML files to process...")
    all_business_data = []
    
    # Use ProcessPoolExecutor to leverage multiple CPU cores. It will default to the number of processors on the machine.
    with ProcessPoolExecutor() as executor:
        future_to_file = {executor.submit(process_single_file, file): file for file in html_files}
        
        print("Processing files in parallel...")
        for i, future in enumerate(as_completed(future_to_file)):
            file_path = future_to_file[future]
            try:
                result = future.result()
                if result:
                    all_business_data.append(result)
                print(f"  Processed {i + 1}/{len(html_files)}: {file_path.name}", end='\r')
            except Exception as exc:
                print(f"\n{file_path.name} generated an exception: {exc}")

    print("\n\nAll files processed.")

    if not all_business_data:
        print("No data was successfully extracted.")
        return

    # Write the consolidated data to the output file
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(all_business_data, f, indent=4, ensure_ascii=False)
        print(f"\n✅ Data from {len(all_business_data)} files successfully extracted and saved to {output_file}")
    except IOError as e:
        print(f"\n❌ Error writing to output file {output_file}: {e}")


def main():
    """Main function to run the parallel extraction process."""
    start_time = time.time()
    try:
        # Directory containing potentially thousands of HTML files
        input_html_directory = r"html-files"
        output_json_path = 'data/all_profiles_data.json'
        
        process_directory_in_parallel(input_html_directory, output_json_path)

    except FileNotFoundError as e:
        print(f"Error: {e}. Please ensure the input directory exists.")
    except Exception as e:
        print(f"An unexpected error occurred in main: {e}")
    finally:
        end_time = time.time()
        print(f"\nTotal execution time: {end_time - start_time:.2f} seconds.")

if __name__ == "__main__":
    main()